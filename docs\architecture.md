# 🏗️ DeerFlow 项目架构文档

DeerFlow 是一个基于现代技术栈构建的深度研究框架，采用前后端分离架构，支持多种LLM模型和研究工具的集成。

## 📋 目录

1. [系统概览](#系统概览)
2. [技术栈](#技术栈)
3. [架构设计](#架构设计)
4. [后端架构](#后端架构)
5. [前端架构](#前端架构)
6. [核心模块](#核心模块)
7. [数据流程](#数据流程)
8. [部署架构](#部署架构)
9. [扩展性设计](#扩展性设计)

## 系统概览

DeerFlow 是一个基于人工智能的深度研究框架，旨在通过整合大语言模型（LLM）和专业工具来实现高效的研究流程。系统采用微服务架构，支持多种部署方式，并提供丰富的扩展接口。

### 核心特性

- 🤖 多LLM模型支持（OpenAI、Qwen、DeepSeek等）
- 🔍 多搜索引擎集成（Tavily、Brave Search、DuckDuckGo、Arxiv）
- 📊 智能工作流管理（基于LangGraph）
- 🎙️ 播客生成功能
- 📑 PPT生成功能
- 🌐 现代化Web界面
- 🐳 容器化部署
- 🔌 MCP（Model Context Protocol）集成

## 技术栈

### 后端技术栈

| 技术分类 | 技术选型 | 版本要求 | 用途说明 |
|---------|---------|---------|---------|
| **运行环境** | Python | 3.12+ | 主要编程语言 |
| **Web框架** | FastAPI | 0.110.0+ | REST API和异步Web服务 |
| **Web服务器** | Uvicorn | 0.27.1+ | ASGI服务器 |
| **AI框架** | LangChain | 0.3.19+ | LLM应用开发框架 |
| **工作流引擎** | LangGraph | 0.3.5+ | 智能Agent工作流管理 |
| **HTTP客户端** | HTTPX | 0.28.1+ | 异步HTTP请求处理 |
| **数据处理** | Pandas | 2.2.3+ | 数据分析和处理 |
| **数值计算** | NumPy | 2.2.3+ | 科学计算支持 |
| **模型接口** | LiteLLM | 1.63.11+ | 多模型统一接口 |
| **搜索引擎** | duckduckgo-search | 8.0.0+ | 搜索功能实现 |
| **文档处理** | Jinja2 | 3.1.3+ | 模板渲染 |
| **配置管理** | python-dotenv | 1.0.1+ | 环境变量管理 |

### 前端技术栈

| 技术分类 | 技术选型 | 版本要求 | 用途说明 |
|---------|---------|---------|---------|
| **运行环境** | Node.js | 22+ | JavaScript运行环境 |
| **包管理器** | pnpm | 10.6.5+ | 依赖包管理 |
| **前端框架** | Next.js | 15.2.3+ | React全栈框架 |
| **UI库** | React | 19.0.0+ | 用户界面组件 |
| **UI组件** | Radix UI | - | 无头UI组件库 |
| **样式框架** | TailwindCSS | 4.0.15+ | 原子化CSS框架 |
| **状态管理** | Zustand | 5.0.3+ | 轻量级状态管理 |
| **动画库** | Framer Motion | 12.6.5+ | 动画和交互效果 |
| **富文本编辑** | Tiptap | 2.11.7+ | 现代富文本编辑器 |
| **流程图** | @xyflow/react | 12.6.0+ | 交互式流程图组件 |
| **表单处理** | React Hook Form | 7.56.1+ | 高性能表单库 |
| **Markdown渲染** | React Markdown | 10.1.0+ | Markdown内容渲染 |
| **语法高亮** | Highlight.js | 11.11.1+ | 代码语法高亮 |
| **数学公式** | KaTeX | 0.16.21+ | 数学公式渲染 |
| **类型检查** | TypeScript | 5.8.2+ | 静态类型检查 |

## 架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "前端层 (Frontend Layer)"
        WEB[Web UI<br/>Next.js + React]
        MOBILE[移动端<br/>React Native<br/>(计划中)]
    end
    
    subgraph "API网关层 (API Gateway Layer)"
        GATEWAY[API Gateway<br/>FastAPI]
        AUTH[身份认证<br/>JWT/OAuth]
        RATE[限流控制<br/>Rate Limiting]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        WORKFLOW[工作流引擎<br/>LangGraph]
        AGENTS[智能代理<br/>LangChain Agents]
        ORCHESTRATOR[任务编排器<br/>Task Orchestrator]
    end
    
    subgraph "服务层 (Service Layer)"
        LLM[LLM服务<br/>Multi-Model Support]
        SEARCH[搜索服务<br/>Multi-Engine]
        CRAWLER[爬虫服务<br/>Web Scraping]
        TTS[语音服务<br/>Text-to-Speech]
        PPT[文档服务<br/>PPT Generation]
        RAG[RAG服务<br/>Retrieval Augmented Generation]
    end
    
    subgraph "数据层 (Data Layer)"
        CACHE[缓存层<br/>Redis]
        VECTOR[向量数据库<br/>Chroma/Faiss]
        FILE[文件存储<br/>Local/S3]
        CONFIG[配置管理<br/>YAML/ENV]
    end
    
    subgraph "外部集成 (External Integrations)"
        LLMAPI[LLM APIs<br/>OpenAI/Qwen/DeepSeek]
        SEARCHAPI[搜索APIs<br/>Tavily/Brave/DuckDuckGo]
        MCP[MCP服务<br/>Model Context Protocol]
        RAGFLOW[RAGFlow<br/>RAG集成]
    end
    
    WEB --> GATEWAY
    MOBILE --> GATEWAY
    GATEWAY --> AUTH
    GATEWAY --> RATE
    GATEWAY --> WORKFLOW
    WORKFLOW --> AGENTS
    WORKFLOW --> ORCHESTRATOR
    AGENTS --> LLM
    AGENTS --> SEARCH
    AGENTS --> CRAWLER
    ORCHESTRATOR --> TTS
    ORCHESTRATOR --> PPT
    ORCHESTRATOR --> RAG
    LLM --> LLMAPI
    SEARCH --> SEARCHAPI
    WORKFLOW --> CACHE
    RAG --> VECTOR
    ORCHESTRATOR --> FILE
    WORKFLOW --> CONFIG
    AGENTS --> MCP
    RAG --> RAGFLOW
```

### 核心设计原则

1. **模块化设计**: 每个功能模块独立开发，降低耦合度
2. **异步处理**: 基于FastAPI和异步编程，提高并发性能
3. **插件化架构**: 支持LLM模型、搜索引擎的插件式集成
4. **流式处理**: 支持实时数据流和增量更新
5. **容错机制**: 内置重试和降级策略
6. **可扩展性**: 支持水平扩展和微服务部署

## 后端架构

### 项目结构

```
src/
├── agents/          # AI代理模块
│   ├── agents.py    # 代理定义和配置
│   └── __init__.py
├── config/          # 配置管理
│   ├── settings.py  # 系统配置
│   └── __init__.py
├── crawler/         # 网页爬虫模块
│   ├── jina.py      # Jina爬虫实现
│   └── __init__.py
├── graph/           # 工作流图模块
│   ├── builder.py   # 图构建器
│   ├── nodes.py     # 节点定义
│   ├── types.py     # 类型定义
│   └── __init__.py
├── llms/            # 大语言模型模块
│   ├── factory.py   # 模型工厂
│   └── __init__.py
├── podcast/         # 播客生成模块
│   ├── graph/       # 播客工作流
│   └── __init__.py
├── ppt/             # PPT生成模块
│   ├── graph/       # PPT工作流
│   └── __init__.py
├── prompts/         # 提示词模板
│   ├── templates/   # 模板文件
│   └── __init__.py
├── prose/           # 文档处理模块
│   └── __init__.py
├── rag/             # RAG集成模块
│   ├── ragflow.py   # RAGFlow集成
│   └── __init__.py
├── server/          # Web服务器模块
│   ├── api/         # API路由
│   ├── middleware/  # 中间件
│   └── __init__.py
├── tools/           # 工具集合
│   ├── search.py    # 搜索工具
│   ├── python.py    # Python执行工具
│   └── __init__.py
├── utils/           # 工具函数
│   ├── logger.py    # 日志工具
│   └── __init__.py
└── workflow.py      # 主工作流程
```

### 核心组件说明

#### 1. 工作流引擎 (LangGraph)

```python
# 工作流节点类型
class WorkflowNode:
    - planner: 规划节点
    - researcher: 研究节点  
    - writer: 写作节点
    - reviewer: 审查节点
    - publisher: 发布节点
```

#### 2. 智能代理系统

```python
# 代理类型
class AgentTypes:
    - researcher: 研究代理
    - writer: 写作代理
    - critic: 评论代理
    - publisher: 发布代理
```

#### 3. LLM集成层

支持多种LLM模型的统一接口：
- OpenAI GPT系列
- Qwen系列模型
- DeepSeek系列
- 自定义模型接口

#### 4. 搜索引擎集成

```python
# 支持的搜索引擎
SEARCH_ENGINES = {
    'tavily': TavilySearch,
    'brave_search': BraveSearch,
    'duckduckgo': DuckDuckGoSearch,
    'arxiv': ArxivSearch
}
```

## 前端架构

### 项目结构

```
web/src/
├── app/                    # Next.js App Router
│   ├── chat/              # 聊天页面
│   ├── landing/           # 首页
│   ├── settings/          # 设置页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页组件
├── components/            # 可复用组件
│   ├── ui/               # 基础UI组件
│   ├── chat/             # 聊天相关组件
│   ├── layout/           # 布局组件
│   └── common/           # 通用组件
├── core/                 # 核心业务逻辑
│   ├── api/              # API接口
│   ├── hooks/            # 自定义Hooks
│   ├── stores/           # 状态管理
│   ├── types/            # 类型定义
│   └── utils/            # 工具函数
├── hooks/                # 全局Hooks
├── lib/                  # 第三方库配置
├── styles/               # 样式文件
├── typings/              # 类型声明
└── env.js                # 环境变量配置
```

### 核心组件说明

#### 1. 状态管理 (Zustand)

```typescript
// 主要状态存储
interface AppState {
  chat: ChatState;      // 聊天状态
  settings: SettingsState; // 设置状态
  workflow: WorkflowState; // 工作流状态
}
```

#### 2. API接口层

```typescript
// API服务类型
interface APIServices {
  chat: ChatAPI;        // 聊天API
  research: ResearchAPI; // 研究API
  workflow: WorkflowAPI; // 工作流API
  settings: SettingsAPI; // 设置API
}
```

#### 3. 组件架构

```
ComponentHierarchy:
├── Layout Components
│   ├── Header
│   ├── Sidebar
│   └── Footer
├── Feature Components
│   ├── ChatInterface
│   ├── WorkflowViewer
│   ├── SettingsPanel
│   └── ResultDisplay
└── UI Components
    ├── Button
    ├── Input
    ├── Modal
    └── Loading
```

## 核心模块

### 1. 工作流管理模块

**功能描述**: 基于LangGraph的智能工作流管理系统

**主要特性**:
- 动态工作流构建
- 节点状态管理
- 异步任务处理
- 流式数据输出

**关键文件**:
- `src/workflow.py`: 主工作流逻辑
- `src/graph/builder.py`: 图构建器
- `src/graph/nodes.py`: 节点定义

### 2. LLM集成模块

**功能描述**: 多模型LLM统一接口

**主要特性**:
- 多模型支持
- 智能负载均衡
- 自动重试机制
- 成本优化

**关键文件**:
- `src/llms/factory.py`: 模型工厂
- `src/config/settings.py`: 模型配置

### 3. 搜索集成模块

**功能描述**: 多搜索引擎集成系统

**主要特性**:
- 多引擎支持
- 结果聚合
- 缓存机制
- 反垃圾过滤

**关键文件**:
- `src/tools/search.py`: 搜索工具
- `src/crawler/jina.py`: 网页爬虫

### 4. RAG集成模块

**功能描述**: 检索增强生成系统

**主要特性**:
- RAGFlow集成
- 向量搜索
- 文档处理
- 上下文增强

**关键文件**:
- `src/rag/ragflow.py`: RAGFlow集成
- 向量数据库接口

### 5. 多媒体生成模块

**功能描述**: 播客和PPT生成系统

**主要特性**:
- 播客音频生成
- PPT自动生成
- 模板系统
- 格式转换

**关键文件**:
- `src/podcast/graph/builder.py`: 播客工作流
- `src/ppt/graph/builder.py`: PPT工作流

## 数据流程

### 用户请求处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web前端
    participant A as API网关
    participant WF as 工作流引擎
    participant LLM as LLM服务
    participant S as 搜索服务
    participant D as 数据库

    U->>W: 提交研究请求
    W->>A: HTTP请求
    A->>WF: 创建工作流
    WF->>LLM: 生成研究计划
    LLM-->>WF: 返回计划
    WF->>S: 执行搜索
    S-->>WF: 返回搜索结果
    WF->>LLM: 生成研究报告
    LLM-->>WF: 返回报告
    WF->>D: 保存结果
    WF-->>A: 流式返回结果
    A-->>W: 实时更新
    W-->>U: 显示结果
```

### 工作流执行流程

```mermaid
graph LR
    A[用户输入] --> B[规划阶段]
    B --> C[背景调研]
    C --> D[计划确认]
    D --> E[执行研究]
    E --> F[内容生成]
    F --> G[质量审查]
    G --> H[结果输出]
    
    B --> I[LLM调用]
    C --> J[搜索引擎]
    E --> K[多工具调用]
    F --> L[文档生成]
    G --> M[质量检查]
```

## 部署架构

### 开发环境部署

```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
    volumes:
      - ./src:/app/src
      
  frontend:
    build: ./web
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - ./web/src:/app/src
```

### 生产环境部署

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  backend:
    image: deer-flow-backend:latest
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
    deploy:
      replicas: 3
      
  frontend:
    image: deer-flow-frontend:latest
    ports:
      - "3000:3000"
    depends_on:
      - backend
      
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
      
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

### 云原生部署

支持在以下平台部署：
- **Kubernetes**: 完整的K8s部署配置
- **Docker Swarm**: 容器集群部署
- **火山引擎**: 一键部署支持
- **AWS/Azure/GCP**: 云平台集成

## 扩展性设计

### 1. 插件系统

```python
# 插件接口定义
class PluginInterface:
    def initialize(self, config: Dict) -> None:
        """插件初始化"""
        pass
        
    def execute(self, context: Dict) -> Dict:
        """插件执行"""
        pass
        
    def cleanup(self) -> None:
        """插件清理"""
        pass
```

### 2. MCP集成

支持Model Context Protocol，实现：
- 外部工具集成
- 上下文协议标准化
- 插件热插拔
- 分布式工具调用

### 3. API扩展

```python
# API扩展接口
@app.route("/api/v1/extensions/{extension_name}")
async def extension_endpoint(extension_name: str):
    """扩展API端点"""
    pass
```

### 4. 前端扩展

```typescript
// 组件扩展系统
interface ComponentExtension {
  name: string;
  component: React.Component;
  route: string;
  permissions: string[];
}
```

## 性能优化

### 1. 后端优化

- **异步处理**: 全面采用异步编程模式
- **连接池**: 数据库和HTTP连接池管理
- **缓存策略**: 多层缓存机制
- **负载均衡**: LLM请求负载均衡

### 2. 前端优化

- **代码分割**: 基于路由的代码分割
- **懒加载**: 组件和图片懒加载
- **状态优化**: Zustand状态管理优化
- **缓存策略**: 浏览器缓存和本地存储

### 3. 网络优化

- **CDN加速**: 静态资源CDN分发
- **HTTP/2**: 启用HTTP/2协议
- **压缩**: Gzip/Brotli压缩
- **缓存头**: 合理的缓存策略

## 监控和日志

### 1. 日志系统

```python
# 日志配置
LOGGING_CONFIG = {
    'version': 1,
    'formatters': {
        'default': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'app.log'
        }
    }
}
```

### 2. 监控指标

- **系统指标**: CPU、内存、磁盘使用率
- **应用指标**: 请求量、响应时间、错误率
- **业务指标**: 工作流执行次数、LLM调用统计

### 3. 告警机制

- **阈值告警**: 基于指标阈值的告警
- **异常告警**: 系统异常自动告警
- **业务告警**: 业务逻辑异常告警

## 安全设计

### 1. 认证授权

- **JWT令牌**: 基于JWT的身份认证
- **OAuth集成**: 支持第三方OAuth登录
- **权限控制**: 基于角色的访问控制(RBAC)

### 2. 数据安全

- **加密传输**: HTTPS/TLS加密
- **数据加密**: 敏感数据加密存储
- **访问控制**: API访问频率限制

### 3. 系统安全

- **输入验证**: 严格的输入验证和过滤
- **SQL注入防护**: ORM和参数化查询
- **XSS防护**: 输出编码和CSP策略

## 总结

DeerFlow采用现代化的技术栈和架构设计，具备以下优势：

1. **高性能**: 异步架构和优化策略保证系统性能
2. **高可用**: 容器化部署和集群化支持
3. **易扩展**: 插件化和模块化设计
4. **易维护**: 清晰的代码结构和完善的文档
5. **安全可靠**: 多层次的安全防护机制

该架构设计支持快速迭代和功能扩展，为DeerFlow的长期发展奠定了坚实的技术基础。 
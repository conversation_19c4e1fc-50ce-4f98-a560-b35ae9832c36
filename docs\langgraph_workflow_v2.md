# 🔄 基于LangGraph的智能工作流管理系统 v2

## 目录

1. [系统概览](#系统概览)
2. [工作流架构图](#工作流架构图)
3. [核心功能实现](#核心功能实现)
4. [节点详细说明](#节点详细说明)
5. [流式处理机制](#流式处理机制)
6. [状态管理](#状态管理)
7. [人机交互与中断机制](#人机交互与中断机制)
8. [深度思考模式](#深度思考模式)
9. [多语言支持](#多语言支持)
10. [部署与扩展](#部署与扩展)

## 系统概览

DeerFlow使用LangGraph构建了一个智能的研究工作流系统，该系统通过多个智能代理节点协同工作，实现从用户查询到最终研究报告的完整流程。

### 核心特性

- 🔄 **动态工作流构建**: 根据研究任务动态构建执行路径
- 📊 **节点状态管理**: 实时跟踪每个节点的执行状态
- ⚡ **异步任务处理**: 基于async/await的并发处理
- 🌊 **流式数据输出**: 实时返回处理结果，提供良好的用户体验
- 🧠 **深度思考模式**: 展示AI的推理过程，增强透明度
- 🌐 **多语言支持**: 支持多种语言的研究报告生成
- 🔌 **模块化设计**: 易于扩展和集成新功能

## 工作流架构图

### 整体流程图

```mermaid
graph TD
    START([开始]) --> COORD[协调器<br/>coordinator]
    
    COORD --> BG_CHOICE{是否启用<br/>背景调研}
    BG_CHOICE -->|是| BG_INV[背景调研<br/>background_investigator]
    BG_CHOICE -->|否| PLANNER[规划器<br/>planner]
    BG_INV --> PLANNER
    
    PLANNER --> PLAN_CHECK{计划是否完整}
    PLAN_CHECK -->|否| HUMAN_FB[人工反馈<br/>human_feedback]
    PLAN_CHECK -->|是| REPORTER[报告生成<br/>reporter]
    
    HUMAN_FB --> FB_CHOICE{反馈类型}
    FB_CHOICE -->|编辑计划| PLANNER
    FB_CHOICE -->|接受计划| RESEARCH_TEAM[研究团队<br/>research_team]
    
    RESEARCH_TEAM --> STEP_TYPE{步骤类型}
    STEP_TYPE -->|研究| RESEARCHER[研究员<br/>researcher]
    STEP_TYPE -->|处理| CODER[编码员<br/>coder]
    STEP_TYPE -->|完成| PLANNER
    
    RESEARCHER --> RESEARCH_TEAM
    CODER --> RESEARCH_TEAM
    REPORTER --> END([结束])
    
    classDef startEnd fill:#e1f5fe
    classDef coordinator fill:#f3e5f5
    classDef planner fill:#e8f5e8
    classDef researcher fill:#fff3e0
    classDef coder fill:#fce4ec
    classDef reporter fill:#e0f2f1
    
    class START,END startEnd
    class COORD coordinator
    class PLANNER,HUMAN_FB planner
    class BG_INV,RESEARCHER researcher
    class CODER coder
    class REPORTER reporter
```

### 详细节点关系图

```mermaid
graph LR
    subgraph "初始化阶段"
        A[coordinator] --> B[background_investigator]
        B --> C[planner]
    end
    
    subgraph "计划阶段"
        C --> D[human_feedback]
        D -->|编辑| C
        D -->|接受| E[research_team]
    end
    
    subgraph "执行阶段"
        E --> F[researcher]
        E --> G[coder]
        F --> E
        G --> E
        E -->|完成| C
    end
    
    subgraph "输出阶段"
        C -->|上下文充足| H[reporter]
        H --> I[END]
    end
```

## 核心功能实现

### 1. 动态工作流构建

DeerFlow通过LangGraph的StateGraph实现动态工作流构建，根据研究需求和执行状态动态决定下一步操作：

```python
def continue_to_running_research_team(state: State):
    """根据当前计划状态动态决定下一个节点"""
    current_plan = state.get("current_plan")
    if not current_plan or not current_plan.steps:
        return "planner"
    if all(step.execution_res for step in current_plan.steps):
        return "planner"
    for step in current_plan.steps:
        if not step.execution_res:
            break
    if step.step_type and step.step_type == StepType.RESEARCH:
        return "researcher"
    if step.step_type and step.step_type == StepType.PROCESSING:
        return "coder"
    return "planner"
```

### 2. 节点状态管理

每个节点通过State对象管理状态，State继承自LangGraph的MessagesState，包含研究过程中的所有关键信息：

```python
class State(MessagesState):
    """State for the agent system, extends MessagesState with next field."""

    # Runtime Variables
    locale: str = "en-US"
    research_topic: str = ""
    observations: list[str] = []
    resources: list[Resource] = []
    plan_iterations: int = 0
    current_plan: Plan | str = None
    final_report: str = ""
    auto_accepted_plan: bool = False
    enable_background_investigation: bool = True
    background_investigation_results: str = None
    reasoning_content: str = None  # 新增：深度思考内容
```

**状态更新机制**:

```python
# 通过Command对象更新状态和控制流程 (LangGraph 0.3.5+)
return Command(
    update={
        "messages": [AIMessage(content=full_response, name="planner")],
        "current_plan": new_plan,
        "reasoning_content": reasoning_content,  # 更新推理内容
    },
    goto="reporter",
)
```

### 3. 异步任务处理

所有节点都支持异步处理，提高系统并发性能：

```python
async def researcher_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team"]]:
    """Researcher node that do research"""
    logger.info("Researcher node is researching.")
    configurable = Configuration.from_runnable_config(config)
    tools = [get_web_search_tool(configurable.max_search_results), crawl_tool]
    retriever_tool = get_retriever_tool(state.get("resources", []))
    if retriever_tool:
        tools.insert(0, retriever_tool)
    return await _setup_and_execute_agent_step(
        state,
        config,
        "researcher",
        tools,
    )
```

## 流式处理机制

### LangGraph流式处理原理

DeerFlow充分利用LangGraph的流式处理能力，通过`astream`方法实现实时数据流：

```python
# 实际项目中的流式执行代码
async for s in graph.astream(
    input=initial_state, config=config, stream_mode=["messages", "updates"]
):
    if isinstance(s, dict) and "messages" in s:
        if len(s["messages"]) <= last_message_cnt:
            continue
        last_message_cnt = len(s["messages"])
        message = s["messages"][-1]
        message.pretty_print()
```

### 流式输出实现

**1. 服务端流式处理**:

```python
@app.post("/api/chat/stream")
async def chat_stream(request: ChatRequest):
    return StreamingResponse(
        _astream_workflow_generator(...),
        media_type="text/event-stream",  # Server-Sent Events
    )

async def _astream_workflow_generator(...):
    """生成器函数，逐步返回执行结果"""
    async for agent, _, event_data in graph.astream(
        input_,
        config=config,
        stream_mode=["messages", "updates"],  # 组合流式模式
        subgraphs=True,
    ):
        if isinstance(event_data, dict):
            # 处理中断事件
            if "__interrupt__" in event_data:
                yield _make_event("interrupt", {...})
            # 处理推理内容
            elif "reasoning_content" in event_data:
                yield _make_event("reasoning", {
                    "reasoning_content": event_data["reasoning_content"]
                })
        else:
            # 处理消息事件
            message_chunk, message_metadata = event_data
            
            # 根据消息类型返回不同事件
            if isinstance(message_chunk, ToolMessage):
                yield _make_event("tool_call_result", event_stream_message)
            elif isinstance(message_chunk, AIMessageChunk):
                if message_chunk.tool_calls:
                    yield _make_event("tool_calls", event_stream_message)
                else:
                    yield _make_event("message_chunk", event_stream_message)
```

**2. 前端流式处理**:

```typescript
export async function* fetchStream(
  url: string,
  init: RequestInit,
): AsyncIterable<StreamEvent> {
  const response = await fetch(url, init);
  const reader = response.body?.pipeThrough(new TextDecoderStream()).getReader();
  
  let buffer = "";
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    buffer += value;
    while (true) {
      const index = buffer.indexOf("\n\n");
      if (index === -1) break;
      
      const chunk = buffer.slice(0, index);
      buffer = buffer.slice(index + 2);
      const event = parseEvent(chunk);
      if (event) yield event;
    }
  }
}
```

### 实时流式输出的关键机制

**1. LangGraph支持多种流式模式**:
- `"values"`: 返回完整状态更新
- `"messages"`: 返回消息增量更新  
- `"updates"`: 返回节点更新
- `["messages", "updates"]`: 组合模式 (项目实际使用)

**2. 异步生成器 + SSE协议**:

```python
# SSE格式输出
def _make_event(event_type: str, data: dict) -> str:
    return f"event: {event_type}\ndata: {json.dumps(data)}\n\n"
```

**3. 状态增量更新**:

```python
# 跟踪消息数量，只返回新增消息
last_message_cnt = 0
async for s in graph.astream(input=initial_state, config=config, stream_mode="values"):
    if isinstance(s, dict) and "messages" in s:
        if len(s["messages"]) <= last_message_cnt:
            continue  # 跳过已处理的消息
        last_message_cnt = len(s["messages"])
        message = s["messages"][-1]  # 获取最新消息
        message.pretty_print()
```

## 人机交互与中断机制

### LangGraph中断机制原理

LangGraph提供了强大的中断机制，允许工作流在特定节点暂停执行，等待外部输入后继续：

```python
from langgraph.types import Command, interrupt

def human_feedback_node(state) -> Command[Literal["planner", "research_team", "reporter", "__end__"]]:
    auto_accepted_plan = state.get("auto_accepted_plan", False)
    if not auto_accepted_plan:
        # 触发中断，等待用户反馈
        feedback = interrupt("Please Review the Plan.")
        
        # 根据用户反馈决定下一步
        if feedback and str(feedback).upper().startswith("[EDIT_PLAN]"):
            return Command(goto="planner")
        elif feedback and str(feedback).upper().startswith("[ACCEPTED]"):
            logger.info("Plan is accepted by user.")
        else:
            raise TypeError(f"Interrupt value of {feedback} is not supported.")
```

### 中断机制的工作流程

```mermaid
sequenceDiagram
    participant Frontend as 前端界面
    participant Server as 后端服务
    participant Graph as LangGraph
    participant Node as human_feedback_node
    
    Frontend->>Server: 发送用户查询
    Server->>Graph: 启动工作流
    Graph->>Node: 执行到人机反馈节点
    Node->>Graph: 调用interrupt("Please Review the Plan.")
    Graph->>Server: 返回中断事件
    Server->>Frontend: 发送interrupt事件 (SSE)
    
    Note over Frontend: 用户界面显示选项按钮<br/>等待用户选择
    
    Frontend->>Server: 用户点击选项 (accepted/edit_plan)
    Server->>Graph: 使用Command(resume=feedback)恢复
    Graph->>Node: 继续执行，获取用户反馈
    Node->>Graph: 根据反馈返回下一个节点
    Graph->>Server: 继续工作流执行
```

### 服务端中断处理

服务端通过检测`__interrupt__`事件来处理中断：

```python
async def _astream_workflow_generator(...):
    # 如果有中断反馈，使用Command(resume=...)恢复执行
    if not auto_accepted_plan and interrupt_feedback:
        resume_msg = f"[{interrupt_feedback}]"
        if messages:
            resume_msg += f" {messages[-1]['content']}"
        input_ = Command(resume=resume_msg)  # 恢复执行
    
    async for agent, _, event_data in graph.astream(...):
        if isinstance(event_data, dict):
            # 检测中断事件
            if "__interrupt__" in event_data:
                yield _make_event(
                    "interrupt",
                    {
                        "thread_id": thread_id,
                        "id": event_data["__interrupt__"][0].ns[0],
                        "role": "assistant",
                        "content": event_data["__interrupt__"][0].value,
                        "finish_reason": "interrupt",
                        "options": [
                            {"text": "Edit plan", "value": "edit_plan"},
                            {"text": "Start research", "value": "accepted"},
                        ],
                    },
                )
```

## 深度思考模式

### 概述

深度思考模式是DeerFlow v2的重要新特性，允许在计划卡片之前展示AI的深度思考过程，以可折叠的方式呈现推理内容。

### 功能特性

- **智能展示逻辑**: 深度思考过程初始展开，当开始接收计划内容时自动折叠
- **分阶段显示**: 思考阶段只显示思考块，思考结束后才显示计划卡片
- **流式支持**: 支持推理内容的实时流式展示
- **视觉状态反馈**: 思考阶段使用蓝色主题突出显示
- **优雅的动画**: 包含平滑的展开/折叠动画效果

### 技术实现

**1. 数据结构扩展**:

```typescript
export interface Message {
  // ... 其他字段
  reasoningContent?: string;
  reasoningContentChunks?: string[];
}

export interface MessageChunkEvent {
  // ... 其他字段
  reasoning_content?: string;
}
```

**2. 组件实现**:

```typescript
export function ThoughtBlock({
  content,
  isLoading,
  isOpen,
  onOpenChange,
}: ThoughtBlockProps) {
  return (
    <Collapsible
      open={isOpen}
      onOpenChange={onOpenChange}
      className="mb-4 rounded-lg border border-blue-200 bg-blue-50 dark:border-blue-900 dark:bg-blue-950"
    >
      <div className="flex items-center justify-between px-4 py-2">
        <div className="flex items-center gap-2">
          <LightbulbIcon className="h-4 w-4 text-blue-500" />
          <h3 className="text-sm font-medium text-blue-700 dark:text-blue-300">
            AI思考过程
          </h3>
        </div>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <ChevronDownIcon className="h-4 w-4" />
            <span className="sr-only">Toggle</span>
          </Button>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent>
        <div className="px-4 pb-4">
          {isLoading && (
            <div className="flex items-center text-sm text-blue-500">
              <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
              思考中...
            </div>
          )}
          <Markdown className="text-sm">{content}</Markdown>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
```

**3. 交互流程**:

```
用户发送问题 (启用深度思考)
    ↓
开始接收 reasoning_content
    ↓
思考块自动展开 + primary 主题 + 加载动画
    ↓
推理内容流式更新
    ↓
开始接收 content (计划内容)
    ↓
思考块自动折叠 + 主题切换
```

## 多语言支持

DeerFlow v2增强了多语言支持能力，可以根据用户设置的语言环境自动调整输出语言：

```python
def planner_node(state: State, config: RunnableConfig):
    """规划器节点，生成完整研究计划"""
    locale = state.get("locale", "en-US")
    
    # 根据语言环境选择提示词模板
    template_name = f"planner_{locale.split('-')[0]}" if locale != "en-US" else "planner"
    messages = apply_prompt_template(template_name, state, configurable)
    
    # 调用LLM生成计划
    response = get_llm_by_type(AGENT_LLM_MAP["planner"]).invoke(messages)
    
    # 解析响应并更新状态
    return Command(
        update={
            "messages": [AIMessage(content=response.content, name="planner")],
            "current_plan": parse_plan(response.content, locale),
            "locale": locale,
        },
        goto="human_feedback",
    )
```

### 支持的语言

- 英语 (en-US) - 默认
- 中文 (zh-CN)
- 日语 (ja-JP)
- 德语 (de-DE)
- 法语 (fr-FR)
- 俄语 (ru-RU)

### 本地化实现

```python
# 提示词模板本地化
TEMPLATES = {
    "planner_en": "templates/planner_en.jinja2",
    "planner_zh": "templates/planner_zh.jinja2",
    "planner_ja": "templates/planner_ja.jinja2",
    "planner_de": "templates/planner_de.jinja2",
    "planner_fr